/*
Task Sync Plugin Styles
Provides consistent styling for all UI components
*/

/* Enhanced Modal Styles */
.task-sync-create-task {
  max-width: 600px;
  max-height: 90vh;
  min-width: 480px;
}

.task-sync-modal-content {
  padding: 0;
}

.task-sync-context-info {
  background: var(--background-secondary);
  border: 1px solid var(--background-modifier-border);
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 20px;
}

.task-sync-context-text {
  margin: 0;
  color: var(--text-accent);
  font-weight: 500;
  font-size: 0.9em;
}

.task-sync-modal-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--background-modifier-border);
}

.task-sync-modal-header h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-normal);
  font-size: 1.2em;
  font-weight: 600;
}

.task-sync-modal-description {
  margin: 0;
  color: var(--text-muted);
  font-size: 0.9em;
  line-height: 1.4;
}

.task-sync-modal-footer {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--background-modifier-border);
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

/* Form Styles */
.task-sync-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.task-sync-form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.task-sync-form-group label {
  font-weight: 600;
  color: var(--text-normal);
}

.task-sync-form-group input,
.task-sync-form-group select,
.task-sync-form-group textarea {
  padding: 0.5rem;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  background: var(--background-primary);
  color: var(--text-normal);
  font-family: var(--font-interface);
}

.task-sync-form-group input:focus,
.task-sync-form-group select:focus,
.task-sync-form-group textarea:focus {
  outline: none;
  border-color: var(--interactive-accent);
  box-shadow: 0 0 0 2px var(--interactive-accent-hover);
}

.task-sync-form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--background-modifier-border);
}

.task-sync-form-actions button {
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.task-sync-form-actions .mod-cancel {
  background: var(--background-secondary);
  color: var(--text-normal);
  border-color: var(--background-modifier-border);
}

.task-sync-form-actions .mod-cancel:hover {
  background: var(--background-modifier-hover);
}

.task-sync-form-actions .mod-cta {
  background: var(--interactive-accent);
  color: var(--text-on-accent);
}

.task-sync-form-actions .mod-cta:hover {
  background: var(--interactive-accent-hover);
}

.task-sync-form-actions .mod-cta:disabled {
  background: var(--background-modifier-border);
  color: var(--text-muted);
  cursor: not-allowed;
}

.task-sync-form-errors {
  background: var(--background-modifier-error);
  border: 1px solid var(--text-error);
  border-radius: 4px;
  padding: 0.75rem;
  margin-bottom: 1rem;
}

.task-sync-form-error {
  color: var(--text-error);
  font-size: 0.9em;
  margin: 0.25rem 0;
}

.task-sync-form-error:first-child {
  margin-top: 0;
}

/* Enhanced modal-specific styles */
.task-sync-required-field {
  border-left: 3px solid var(--interactive-accent) !important;
}

.task-sync-error {
  background: var(--background-modifier-error);
  color: var(--text-error);
  border: 1px solid var(--background-modifier-error-hover);
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 500;
}

/* Setting component overrides for better modal integration */
.task-sync-modal-content .setting-item {
  border: none;
  padding: 12px 0;
}

.task-sync-modal-content .setting-item:not(:last-child) {
  border-bottom: 1px solid var(--background-modifier-border-hover);
}

.task-sync-modal-content .setting-item-info {
  margin-right: 16px;
}

.task-sync-modal-content .setting-item-name {
  font-weight: 500;
  color: var(--text-normal);
}

.task-sync-modal-content .setting-item-description {
  color: var(--text-muted);
  font-size: 13px;
  margin-top: 2px;
}

.task-sync-modal-content .setting-item-control {
  min-width: 200px;
}

.task-sync-modal-content .setting-item-control input,
.task-sync-modal-content .setting-item-control select,
.task-sync-modal-content .setting-item-control textarea {
  width: 100%;
}

.task-sync-form-error:last-child {
  margin-bottom: 0;
}

/* Loading, Error, and Success States */
.task-sync-modal-loading,
.task-sync-modal-error,
.task-sync-modal-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.task-sync-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--background-modifier-border);
  border-top: 3px solid var(--interactive-accent);
  border-radius: 50%;
  animation: task-sync-spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes task-sync-spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.task-sync-error-icon,
.task-sync-success-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

/* Tab Styles */
.task-sync-modal-tabs {
  display: flex;
  flex-direction: column;
}

.task-sync-tab-headers {
  display: flex;
  border-bottom: 1px solid var(--background-modifier-border);
  margin-bottom: 1rem;
}

.task-sync-tab-header {
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  cursor: pointer;
  color: var(--text-muted);
  font-weight: 500;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.task-sync-tab-header:hover {
  color: var(--text-normal);
  background: var(--background-modifier-hover);
}

.task-sync-tab-header.active {
  color: var(--interactive-accent);
  border-bottom-color: var(--interactive-accent);
}

.task-sync-tab-content {
  flex: 1;
}

/* Info Box Styles */
.task-sync-info-box {
  padding: 0.75rem;
  border-radius: 4px;
  margin: 1rem 0;
  border-left: 4px solid;
}

.task-sync-info-info {
  background: var(--background-secondary);
  border-left-color: var(--interactive-accent);
  color: var(--text-normal);
}

.task-sync-info-warning {
  background: var(--background-modifier-error-hover);
  border-left-color: var(--text-warning);
  color: var(--text-warning);
}

.task-sync-info-error {
  background: var(--background-modifier-error);
  border-left-color: var(--text-error);
  color: var(--text-error);
}

/* Divider */
.task-sync-divider {
  border: none;
  height: 1px;
  background: var(--background-modifier-border);
  margin: 1.5rem 0;
}

/* Button Overrides */
.task-sync-modal button {
  font-family: var(--font-interface);
}

/* Responsive Design */
@media (max-width: 768px) {
  .task-sync-modal {
    max-width: 95vw;
    min-width: 300px;
  }

  .task-sync-tab-headers {
    flex-wrap: wrap;
  }

  .task-sync-tab-header {
    flex: 1;
    min-width: 120px;
  }
}

/* Dashboard Styles */
.task-sync-dashboard {
  max-width: 1000px;
  max-height: 90vh;
}

.task-sync-dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--background-modifier-border);
}

.task-sync-dashboard-tabs {
  display: flex;
  gap: 0.5rem;
}

.task-sync-dashboard-tab {
  background: none;
  border: 1px solid var(--background-modifier-border);
  border-radius: 6px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  color: var(--text-muted);
  font-weight: 500;
  transition: all 0.2s ease;
}

.task-sync-dashboard-tab:hover {
  color: var(--text-normal);
  background: var(--background-modifier-hover);
}

.task-sync-dashboard-tab.active {
  color: var(--interactive-accent);
  background: var(--interactive-accent-hover);
  border-color: var(--interactive-accent);
}

.task-sync-dashboard-actions {
  display: flex;
  gap: 0.5rem;
}

.task-sync-dashboard-content {
  max-height: 500px;
  overflow-y: auto;
}

/* Statistics Cards */
.task-sync-dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.task-sync-stat-card {
  background: var(--background-secondary);
  border: 1px solid var(--background-modifier-border);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  transition: transform 0.2s ease;
}

.task-sync-stat-card:hover {
  transform: translateY(-2px);
}

.task-sync-stat-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.task-sync-stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-normal);
  margin-bottom: 0.25rem;
}

.task-sync-stat-title {
  font-size: 0.9rem;
  color: var(--text-muted);
  font-weight: 500;
}

/* Dashboard Sections */
.task-sync-dashboard-section {
  margin-bottom: 2rem;
}

.task-sync-dashboard-section h3 {
  margin: 0 0 1rem 0;
  color: var(--text-normal);
  font-size: 1.1rem;
  font-weight: 600;
}

/* Filters */
.task-sync-dashboard-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--background-secondary);
  border-radius: 6px;
  flex-wrap: wrap;
}

.task-sync-filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  min-width: 150px;
}

.task-sync-filter-group label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-muted);
}

.task-sync-filter-group input,
.task-sync-filter-group select {
  padding: 0.4rem;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  background: var(--background-primary);
  color: var(--text-normal);
  font-size: 0.9rem;
}

/* Task List */
.task-sync-task-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.task-sync-task-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background: var(--background-secondary);
  border: 1px solid var(--background-modifier-border);
  border-radius: 6px;
  transition: all 0.2s ease;
}

.task-sync-task-item:hover {
  border-color: var(--interactive-accent-hover);
  transform: translateY(-1px);
}

.task-sync-task-status {
  font-size: 1.2rem;
  margin-top: 0.1rem;
}

.task-sync-task-content {
  flex: 1;
}

.task-sync-task-name {
  font-weight: 600;
  color: var(--text-normal);
  margin-bottom: 0.25rem;
}

.task-sync-task-description {
  font-size: 0.9rem;
  color: var(--text-muted);
  line-height: 1.4;
}

.task-sync-task-meta {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
  flex-wrap: wrap;
}

.task-sync-priority {
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.task-sync-priority-low {
  background: var(--color-green-rgb);
  color: white;
}

.task-sync-priority-medium {
  background: var(--color-yellow-rgb);
  color: black;
}

.task-sync-priority-high {
  background: var(--color-orange-rgb);
  color: white;
}

.task-sync-priority-urgent {
  background: var(--color-red-rgb);
  color: white;
}

.task-sync-deadline {
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  background: var(--background-modifier-border);
  color: var(--text-muted);
}

.task-sync-deadline.overdue {
  background: var(--color-red-rgb);
  color: white;
}

.task-sync-task-actions {
  display: flex;
  gap: 0.25rem;
}

.task-sync-task-actions button {
  padding: 0.3rem 0.6rem;
  font-size: 0.8rem;
}

/* Project List */
.task-sync-project-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.task-sync-project-item {
  padding: 1.5rem;
  background: var(--background-secondary);
  border: 1px solid var(--background-modifier-border);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.task-sync-project-item:hover {
  border-color: var(--interactive-accent-hover);
  transform: translateY(-1px);
}

.task-sync-project-name {
  margin: 0 0 0.5rem 0;
  color: var(--text-normal);
  font-size: 1.1rem;
  font-weight: 600;
}

.task-sync-project-description {
  margin: 0 0 1rem 0;
  color: var(--text-muted);
  line-height: 1.4;
}

.task-sync-project-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--background-modifier-border);
}

.task-sync-project-actions {
  display: flex;
  gap: 0.5rem;
}

/* Area List */
.task-sync-area-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.task-sync-area-item {
  padding: 1.5rem;
  background: var(--background-secondary);
  border: 1px solid var(--background-modifier-border);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.task-sync-area-item:hover {
  border-color: var(--interactive-accent-hover);
  transform: translateY(-1px);
}

.task-sync-area-name {
  margin: 0 0 0.5rem 0;
  color: var(--text-normal);
  font-size: 1.1rem;
  font-weight: 600;
}

.task-sync-area-description {
  margin: 0 0 1rem 0;
  color: var(--text-muted);
  line-height: 1.4;
}

.task-sync-area-meta {
  color: var(--text-muted);
  font-size: 0.9rem;
}

/* Empty State */
.task-sync-empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--text-muted);
}

.task-sync-empty-state h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-normal);
  font-size: 1.1rem;
}

.task-sync-empty-state p {
  margin: 0;
  line-height: 1.4;
}

/* Responsive Dashboard */
@media (max-width: 768px) {
  .task-sync-dashboard-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .task-sync-dashboard-tabs {
    justify-content: center;
  }

  .task-sync-dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .task-sync-dashboard-filters {
    flex-direction: column;
  }

  .task-sync-task-item {
    flex-direction: column;
    gap: 0.5rem;
  }

  .task-sync-task-meta {
    margin-top: 0.75rem;
  }

  .task-sync-task-actions {
    align-self: flex-end;
  }
}

/* Picker Component Styles */
.task-sync-picker-group {
  margin-bottom: 1rem;
}

.task-sync-picker-group .setting-item {
  border: none;
  padding: 0;
}

.task-sync-picker-group .setting-item-info {
  margin-bottom: 0.5rem;
}

.task-sync-picker-group .setting-item-control {
  margin-top: 0.5rem;
}

.task-sync-picker-group select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  background: var(--background-primary);
  color: var(--text-normal);
  font-family: var(--font-interface);
}

.task-sync-picker-group select:focus {
  outline: none;
  border-color: var(--interactive-accent);
  box-shadow: 0 0 0 2px var(--interactive-accent-hover);
}

.task-sync-picker-group select option {
  background: var(--background-primary);
  color: var(--text-normal);
}

/* Special styling for "Create New" options */
.task-sync-picker-group select option[value="__create_new__"] {
  color: var(--interactive-accent);
  font-weight: 500;
}

/* Enhanced Settings Panel Styles */
.task-sync-settings {
  max-width: 800px;
  margin: 0 auto;
}

.task-sync-settings-header {
  margin-bottom: 2rem;
  text-align: center;
}

.task-sync-settings-header h2 {
  margin: 0 0 0.5rem 0;
  color: var(--text-normal);
  font-size: 1.5rem;
}

.task-sync-settings-description {
  margin: 0;
  color: var(--text-muted);
  font-size: 0.95rem;
  line-height: 1.4;
}

.task-sync-settings-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.task-sync-settings-section {
  padding: 0;
  margin-bottom: 2rem;
}

.task-sync-section-header {
  margin: 0 0 1.5rem 0;
  padding: 0;
  color: var(--text-normal);
  font-size: 1.2rem;
  font-weight: 600;
  border-bottom: 1px solid var(--background-modifier-border);
  padding-bottom: 0.5rem;
}

.task-sync-settings-section-desc {
  margin: 0 0 1.5rem 0;
  color: var(--text-muted);
  font-size: 0.9rem;
  line-height: 1.4;
}

.task-sync-settings-info {
  margin: 1.5rem 0;
  padding: 1rem;
  background: var(--background-secondary);
  border-radius: 4px;
  border-left: 3px solid var(--color-blue);
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Section spacing and visual separation */
.task-sync-settings-section:not(:last-child) {
  border-bottom: 1px solid var(--background-modifier-border-hover);
  padding-bottom: 2rem;
}

.task-sync-settings-section .setting-item {
  border: none;
  padding: 0.75rem 0;
}

.task-sync-settings-section .setting-item:not(:last-child) {
  border-bottom: 1px solid var(--background-modifier-border-hover);
}

.task-sync-settings-placeholder {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--text-muted);
}

.task-sync-settings-placeholder p {
  margin: 0;
  font-style: italic;
}

/* Setting validation styles */
.task-sync-setting-error {
  border-left: 3px solid var(--text-error) !important;
  background: var(--background-modifier-error-hover) !important;
}

.task-sync-setting-error .setting-item-description {
  color: var(--text-error) !important;
}

/* Responsive settings */
@media (max-width: 768px) {
  .task-sync-settings-tab-headers {
    flex-wrap: wrap;
  }

  .task-sync-settings-tab-header {
    flex: 1;
    min-width: 120px;
    padding: 0.75rem 1rem;
  }
}

/* Task Creation Modal Styles */
.task-sync-create-task {
  max-width: 650px;
}

.task-sync-picker-section {
  margin: 1.5rem 0;
  padding: 1rem;
  background: var(--background-secondary);
  border-radius: 6px;
  border: 1px solid var(--background-modifier-border);
}

.task-sync-picker-section h4 {
  margin: 0 0 0.5rem 0;
  color: var(--text-normal);
  font-size: 1rem;
  font-weight: 600;
}

.task-sync-section-description {
  margin: 0 0 1rem 0;
  color: var(--text-muted);
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Form field improvements for task creation */
.task-sync-create-task .task-sync-form-group textarea {
  min-height: 80px;
  resize: vertical;
}

.task-sync-create-task .task-sync-form-group input[type="date"] {
  font-family: var(--font-interface);
}

.task-sync-create-task .task-sync-form-group select {
  text-transform: capitalize;
}

/* Form layout improvements */
.task-sync-create-task .task-sync-form {
  max-height: 500px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.task-sync-create-task .task-sync-form::-webkit-scrollbar {
  width: 6px;
}

.task-sync-create-task .task-sync-form::-webkit-scrollbar-track {
  background: var(--background-secondary);
  border-radius: 3px;
}

.task-sync-create-task .task-sync-form::-webkit-scrollbar-thumb {
  background: var(--background-modifier-border);
  border-radius: 3px;
}

.task-sync-create-task .task-sync-form::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}



.task-sync-button-group {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  margin-top: 1rem;
}

.task-sync-dashboard {
  padding: 1rem;
}

.task-sync-dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--background-modifier-border);
}

.task-sync-dashboard-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-normal);
}

.task-sync-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.task-sync-stat-card {
  padding: 1rem;
  background: var(--background-secondary);
  border-radius: 8px;
  border: 1px solid var(--background-modifier-border);
}

.task-sync-stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--interactive-accent);
}

.task-sync-stat-label {
  font-size: 0.875rem;
  color: var(--text-muted);
  margin-top: 0.25rem;
}
